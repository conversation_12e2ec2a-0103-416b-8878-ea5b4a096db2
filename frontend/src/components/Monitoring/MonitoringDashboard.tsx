import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Progress,
  Table,
  Tag,
  Select,
  DatePicker,
  Space,
  Alert,
  Button,
  Switch,
} from 'antd';
import { monitoringApi } from '../../services/api';
import {
  <PERSON><PERSON>hart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  AreaChart,
  Area,
} from 'recharts';
import {
  WarningOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ReloadOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';

const { Option } = Select;
const { RangePicker } = DatePicker;

interface SystemMetrics {
  timestamp: string;
  cpu_usage: number;
  memory_usage: number;
  disk_usage: number;
  network_in: number;
  network_out: number;
}

interface ServerStatus {
  id: number;
  name: string;
  status: 'online' | 'offline' | 'warning';
  cpu_usage: number;
  memory_usage: number;
  disk_usage: number;
  last_update: string;
}

interface AlertRecord {
  id: number;
  server_name: string;
  alert_type: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  created_at: string;
  status: 'active' | 'resolved';
}

const MonitoringDashboard: React.FC = () => {
  const [selectedServer, setSelectedServer] = useState<number>(1);
  const [metricsData, setMetricsData] = useState<SystemMetrics[]>([]);
  const [serverStatuses, setServerStatuses] = useState<ServerStatus[]>([]);
  const [alerts, setAlerts] = useState<AlertRecord[]>([]);
  const [loading, setLoading] = useState(false);
  const [refreshInterval, setRefreshInterval] = useState(10); // 默认10秒
  const [autoRefresh, setAutoRefresh] = useState(true);

  // 加载监控数据
  const loadMonitoringData = async () => {
    try {
      setLoading(true);

      // 并行加载服务器状态和告警
      const [serverResponse, alertResponse] = await Promise.all([
        monitoringApi.getServerMonitoring(),
        monitoringApi.getAlerts()
      ]);

      // API响应拦截器已经返回了data，所以response直接就是数据
      setServerStatuses(Array.isArray(serverResponse) ? serverResponse : []);
      setAlerts(Array.isArray(alertResponse) ? alertResponse : []);

      // 如果有选中的服务器，加载详细指标
      if (selectedServer) {
        const metricsResponse = await monitoringApi.getServerStats(selectedServer);
        setMetricsData(Array.isArray(metricsResponse) ? metricsResponse : []);
      }
    } catch (error) {
      console.error('加载监控数据失败:', error);
      // 不再使用模拟数据，保持空状态
      setServerStatuses([]);
      setMetricsData([]);
      setAlerts([]);
    } finally {
      setLoading(false);
    }
  };



  // 初始化数据
  useEffect(() => {
    loadMonitoringData();
  }, [selectedServer]);

  // 定时刷新
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      loadMonitoringData();
    }, refreshInterval * 1000);

    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval]);



  // 告警操作函数
  const handleAcknowledgeAlert = async (alertId: number) => {
    try {
      await monitoringApi.acknowledgeAlert(alertId);
      loadMonitoringData(); // 重新加载数据
    } catch (error) {
      console.error('确认告警失败:', error);
    }
  };

  const handleResolveAlert = async (alertId: number) => {
    try {
      await monitoringApi.resolveAlert(alertId);
      loadMonitoringData(); // 重新加载数据
    } catch (error) {
      console.error('解决告警失败:', error);
    }
  };

  const serverColumns: ColumnsType<ServerStatus> = [
    {
      title: '服务器名称',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <Space>
          <span>{text}</span>
          {record.status === 'online' && <CheckCircleOutlined style={{ color: '#52c41a' }} />}
          {record.status === 'warning' && <WarningOutlined style={{ color: '#faad14' }} />}
          {record.status === 'offline' && <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />}
        </Space>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const colorMap: { [key: string]: string } = {
          online: 'green',
          warning: 'orange',
          offline: 'red',
        };
        const textMap: { [key: string]: string } = {
          online: '在线',
          warning: '警告',
          offline: '离线',
        };
        return <Tag color={colorMap[status]}>{textMap[status]}</Tag>;
      },
    },
    {
      title: 'CPU使用率',
      dataIndex: 'cpu_usage',
      key: 'cpu_usage',
      render: (value) => (
        <Progress 
          percent={Math.round(value)} 
          size="small" 
          status={value > 80 ? 'exception' : 'normal'}
        />
      ),
    },
    {
      title: '内存使用率',
      dataIndex: 'memory_usage',
      key: 'memory_usage',
      render: (value) => (
        <Progress 
          percent={Math.round(value)} 
          size="small" 
          status={value > 85 ? 'exception' : 'normal'}
        />
      ),
    },
    {
      title: '磁盘使用率',
      dataIndex: 'disk_usage',
      key: 'disk_usage',
      render: (value) => (
        <Progress 
          percent={Math.round(value)} 
          size="small" 
          status={value > 90 ? 'exception' : 'normal'}
        />
      ),
    },
    {
      title: '最后更新',
      dataIndex: 'last_update',
      key: 'last_update',
      render: (text) => new Date(text).toLocaleString(),
    },
  ];

  const alertColumns: ColumnsType<AlertRecord> = [
    {
      title: '服务器',
      dataIndex: 'server_name',
      key: 'server_name',
    },
    {
      title: '告警类型',
      dataIndex: 'alert_type',
      key: 'alert_type',
      render: (text) => {
        const typeMap: { [key: string]: string } = {
          cpu_high: 'CPU过高',
          memory_high: '内存过高',
          disk_high: '磁盘过高',
          server_offline: '服务器离线',
        };
        return typeMap[text] || text;
      },
    },
    {
      title: '严重程度',
      dataIndex: 'severity',
      key: 'severity',
      render: (severity: string) => {
        const colorMap: { [key: string]: string } = {
          low: 'blue',
          medium: 'orange',
          high: 'red',
          critical: 'purple',
        };
        const textMap: { [key: string]: string } = {
          low: '低',
          medium: '中',
          high: '高',
          critical: '严重',
        };
        return <Tag color={colorMap[severity]}>{textMap[severity]}</Tag>;
      },
    },
    {
      title: '告警消息',
      dataIndex: 'message',
      key: 'message',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={status === 'active' ? 'red' : 'green'}>
          {status === 'active' ? '活跃' : '已解决'}
        </Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (text) => new Date(text).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="small">
          {record.status === 'active' && (
            <>
              <Button
                size="small"
                onClick={() => handleAcknowledgeAlert(record.id)}
              >
                确认
              </Button>
              <Button
                size="small"
                type="primary"
                onClick={() => handleResolveAlert(record.id)}
              >
                解决
              </Button>
            </>
          )}
        </Space>
      ),
    },
  ];

  const formatChartData = (data: SystemMetrics[]) => {
    return data.map(item => ({
      ...item,
      time: new Date(item.timestamp).toLocaleTimeString(),
    }));
  };

  const onlineServers = serverStatuses.filter(s => s.status === 'online').length;
  const warningServers = serverStatuses.filter(s => s.status === 'warning').length;
  const offlineServers = serverStatuses.filter(s => s.status === 'offline').length;
  const activeAlerts = alerts.filter(a => a.status === 'active').length;

  return (
    <div>
      {/* 监控控制面板 */}
      <Card style={{ marginBottom: 16 }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Space>
              <span>自动刷新:</span>
              <Switch
                checked={autoRefresh}
                onChange={setAutoRefresh}
                checkedChildren="开"
                unCheckedChildren="关"
              />
              <span>刷新间隔:</span>
              <Select
                value={refreshInterval}
                onChange={setRefreshInterval}
                style={{ width: 100 }}
                disabled={!autoRefresh}
              >
                <Option value={5}>5秒</Option>
                <Option value={10}>10秒</Option>
                <Option value={30}>30秒</Option>
                <Option value={60}>1分钟</Option>
              </Select>
            </Space>
          </Col>
          <Col>
            <Space>
              <Button
                icon={<ReloadOutlined />}
                onClick={loadMonitoringData}
                loading={loading}
              >
                手动刷新
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 告警横幅 */}
      {activeAlerts > 0 && (
        <Alert
          message={`当前有 ${activeAlerts} 个活跃告警需要处理`}
          type="warning"
          showIcon
          closable
          style={{ marginBottom: 16 }}
        />
      )}

      {/* 概览统计 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="在线服务器"
              value={onlineServers}
              valueStyle={{ color: '#52c41a' }}
              prefix={<CheckCircleOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="警告服务器"
              value={warningServers}
              valueStyle={{ color: '#faad14' }}
              prefix={<WarningOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="离线服务器"
              value={offlineServers}
              valueStyle={{ color: '#ff4d4f' }}
              prefix={<ExclamationCircleOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="活跃告警"
              value={activeAlerts}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 图表区域 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={24}>
          <Card
            title="系统资源监控"
            extra={
              <Space>
                <Select
                  value={selectedServer}
                  onChange={setSelectedServer}
                  style={{ width: 200 }}
                >
                  <Option value={1}>测试服务器1</Option>
                  <Option value={2}>生产服务器1</Option>
                </Select>
                <RangePicker showTime />
              </Space>
            }
          >
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={formatChartData(metricsData)}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="time" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Line 
                  type="monotone" 
                  dataKey="cpu_usage" 
                  stroke="#8884d8" 
                  name="CPU使用率(%)"
                />
                <Line 
                  type="monotone" 
                  dataKey="memory_usage" 
                  stroke="#82ca9d" 
                  name="内存使用率(%)"
                />
                <Line 
                  type="monotone" 
                  dataKey="disk_usage" 
                  stroke="#ffc658" 
                  name="磁盘使用率(%)"
                />
              </LineChart>
            </ResponsiveContainer>
          </Card>
        </Col>
      </Row>

      {/* 网络流量图表 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={24}>
          <Card title="网络流量监控">
            <ResponsiveContainer width="100%" height={250}>
              <AreaChart data={formatChartData(metricsData)}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="time" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Area 
                  type="monotone" 
                  dataKey="network_in" 
                  stackId="1"
                  stroke="#8884d8" 
                  fill="#8884d8"
                  name="入站流量(KB/s)"
                />
                <Area 
                  type="monotone" 
                  dataKey="network_out" 
                  stackId="1"
                  stroke="#82ca9d" 
                  fill="#82ca9d"
                  name="出站流量(KB/s)"
                />
              </AreaChart>
            </ResponsiveContainer>
          </Card>
        </Col>
      </Row>

      {/* 服务器状态表格 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={24}>
          <Card title="服务器状态">
            <Table
              columns={serverColumns}
              dataSource={serverStatuses}
              rowKey="id"
              pagination={false}
            />
          </Card>
        </Col>
      </Row>

      {/* 告警列表 */}
      <Row gutter={16}>
        <Col span={24}>
          <Card title="告警列表">
            <Table
              columns={alertColumns}
              dataSource={alerts}
              rowKey="id"
              pagination={{ pageSize: 10 }}
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default MonitoringDashboard;
