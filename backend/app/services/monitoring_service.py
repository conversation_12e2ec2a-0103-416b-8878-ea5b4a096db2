"""
监控服务模块
"""
import asyncio
import time
from datetime import datetime
from typing import Dict, List, Optional, Any
from dao import ServerDAO
import logging

logger = logging.getLogger(__name__)


class MonitoringService:
    """监控服务"""
    
    def __init__(self):
        self.monitoring_data: Dict[int, Dict[str, Any]] = {}
        self.monitoring_interval = 10  # 默认10秒
        self.running = False
        self.monitoring_task = None
    
    async def start(self):
        """启动监控服务"""
        if self.running:
            logger.warning("监控服务已在运行中")
            return
        
        self.running = True
        self.monitoring_task = asyncio.create_task(self._monitoring_loop())
        logger.info("监控服务已启动")
    
    async def stop(self):
        """停止监控服务"""
        self.running = False
        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
        logger.info("监控服务已停止")
    
    async def _monitoring_loop(self):
        """监控循环"""
        while self.running:
            try:
                await self._update_all_servers_monitoring()
                await asyncio.sleep(self.monitoring_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"监控循环发生错误: {e}")
                await asyncio.sleep(self.monitoring_interval)
    
    async def _update_all_servers_monitoring(self):
        """更新所有服务器的监控数据"""
        try:
            # 获取所有服务器
            servers = ServerDAO.get_all()
            
            # 并发更新所有服务器的监控数据
            tasks = []
            for server_basic in servers:
                server = ServerDAO.get_by_id(server_basic['id'])
                if server:
                    task = asyncio.create_task(self._update_server_monitoring(server))
                    tasks.append(task)
            
            if tasks:
                await asyncio.gather(*tasks, return_exceptions=True)
                
        except Exception as e:
            logger.error(f"更新服务器监控数据失败: {e}")
    
    async def _update_server_monitoring(self, server: Dict[str, Any]):
        """更新单个服务器的监控数据"""
        server_id = server['id']

        try:
            # 创建服务器对象用于连接测试
            class SimpleServer:
                def __init__(self, **kwargs):
                    for key, value in kwargs.items():
                        setattr(self, key, value)

            server_obj = SimpleServer(**server)

            # 解密密码
            password = server.get("password", "")
            try:
                from database import decrypt_password
                if password:
                    decrypted_password = decrypt_password(password)
                    password = decrypted_password
            except Exception as e:
                logger.warning(f"服务器 {server.get('name', 'unknown')} 密码解密失败: {e}")

            # 使用简单的连接测试方法
            import paramiko
            import asyncio
            from concurrent.futures import ThreadPoolExecutor

            def test_ssh_connection(host, port, username, password, private_key=None):
                """测试SSH连接"""
                try:
                    client = paramiko.SSHClient()
                    client.set_missing_host_key_policy(paramiko.AutoAddPolicy())

                    if private_key:
                        # 使用私钥认证
                        from io import StringIO
                        key_file = StringIO(private_key)
                        pkey = paramiko.RSAKey.from_private_key(key_file)
                        client.connect(
                            hostname=host,
                            port=port,
                            username=username,
                            pkey=pkey,
                            timeout=10
                        )
                    else:
                        # 使用密码认证
                        client.connect(
                            hostname=host,
                            port=port,
                            username=username,
                            password=password,
                            timeout=10
                        )

                    client.close()
                    return {"success": True}
                except Exception as e:
                    return {"success": False, "error": str(e)}

            # 异步执行连接测试
            executor = ThreadPoolExecutor(max_workers=10)
            loop = asyncio.get_event_loop()
            connection_result = await loop.run_in_executor(
                executor,
                test_ssh_connection,
                server["host"],
                server["port"],
                server["username"],
                password,
                server.get("private_key")
            )

            if connection_result['success']:
                # 获取系统监控数据
                monitoring_data = await self._collect_system_metrics(server_obj)
                monitoring_data.update({
                    "status": "online",
                    "last_update": datetime.now().isoformat(),
                    "connection_error": None
                })
            else:
                # 连接失败
                monitoring_data = {
                    "status": "offline",
                    "cpu_usage": 0.0,
                    "memory_usage": 0.0,
                    "disk_usage": 0.0,
                    "last_update": datetime.now().isoformat(),
                    "connection_error": connection_result.get('error', '连接失败')
                }

            # 更新内存中的监控数据
            self.monitoring_data[server_id] = {
                **server,  # 包含服务器基本信息
                **monitoring_data  # 包含监控数据
            }

            # 移除敏感信息
            if 'password' in self.monitoring_data[server_id]:
                del self.monitoring_data[server_id]['password']
            if 'private_key' in self.monitoring_data[server_id]:
                del self.monitoring_data[server_id]['private_key']
            if 'jump_password' in self.monitoring_data[server_id]:
                del self.monitoring_data[server_id]['jump_password']
            if 'jump_private_key' in self.monitoring_data[server_id]:
                del self.monitoring_data[server_id]['jump_private_key']

        except Exception as e:
            logger.error(f"更新服务器 {server.get('name', 'unknown')} 监控数据失败: {e}")
            # 标记为离线
            self.monitoring_data[server_id] = {
                **server,
                "status": "offline",
                "cpu_usage": 0.0,
                "memory_usage": 0.0,
                "disk_usage": 0.0,
                "last_update": datetime.now().isoformat(),
                "connection_error": str(e)
            }
            # 移除敏感信息
            if 'password' in self.monitoring_data[server_id]:
                del self.monitoring_data[server_id]['password']
            if 'private_key' in self.monitoring_data[server_id]:
                del self.monitoring_data[server_id]['private_key']
            if 'jump_password' in self.monitoring_data[server_id]:
                del self.monitoring_data[server_id]['jump_password']
            if 'jump_private_key' in self.monitoring_data[server_id]:
                del self.monitoring_data[server_id]['jump_private_key']
    
    async def _collect_system_metrics(self, server_obj) -> Dict[str, Any]:
        """收集系统监控指标"""
        try:
            # 使用简单的SSH执行命令方法
            import paramiko
            import asyncio
            from concurrent.futures import ThreadPoolExecutor

            def execute_ssh_command(host, port, username, password, private_key, command):
                """执行SSH命令"""
                try:
                    client = paramiko.SSHClient()
                    client.set_missing_host_key_policy(paramiko.AutoAddPolicy())

                    if private_key:
                        # 使用私钥认证
                        from io import StringIO
                        key_file = StringIO(private_key)
                        pkey = paramiko.RSAKey.from_private_key(key_file)
                        client.connect(
                            hostname=host,
                            port=port,
                            username=username,
                            pkey=pkey,
                            timeout=30
                        )
                    else:
                        # 使用密码认证
                        client.connect(
                            hostname=host,
                            port=port,
                            username=username,
                            password=password,
                            timeout=30
                        )

                    stdin, stdout, stderr = client.exec_command(command, timeout=300)
                    exit_code = stdout.channel.recv_exit_status()
                    stdout_data = stdout.read().decode('utf-8')
                    stderr_data = stderr.read().decode('utf-8')

                    client.close()
                    return exit_code, stdout_data, stderr_data
                except Exception as e:
                    return -1, "", str(e)

            # 解密密码
            password = getattr(server_obj, 'password', '')
            try:
                from database import decrypt_password
                if password:
                    password = decrypt_password(password)
            except Exception:
                pass

            executor = ThreadPoolExecutor(max_workers=3)
            loop = asyncio.get_event_loop()

            # 并发执行命令
            cpu_cmd = "top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | sed 's/%us,//'"
            mem_cmd = "free | grep Mem | awk '{printf \"%.2f\", $3/$2 * 100.0}'"
            disk_cmd = "df -h / | awk 'NR==2{printf \"%.2f\", $5}' | sed 's/%//'"

            tasks = [
                loop.run_in_executor(executor, execute_ssh_command,
                                   server_obj.host, server_obj.port, server_obj.username,
                                   password, getattr(server_obj, 'private_key', None), cpu_cmd),
                loop.run_in_executor(executor, execute_ssh_command,
                                   server_obj.host, server_obj.port, server_obj.username,
                                   password, getattr(server_obj, 'private_key', None), mem_cmd),
                loop.run_in_executor(executor, execute_ssh_command,
                                   server_obj.host, server_obj.port, server_obj.username,
                                   password, getattr(server_obj, 'private_key', None), disk_cmd)
            ]

            results = await asyncio.gather(*tasks)
            cpu_result, mem_result, disk_result = results

            # 解析结果
            cpu_usage = float(cpu_result[1].strip()) if cpu_result[0] == 0 and cpu_result[1].strip() else 0.0
            memory_usage = float(mem_result[1].strip()) if mem_result[0] == 0 and mem_result[1].strip() else 0.0
            disk_usage = float(disk_result[1].strip()) if disk_result[0] == 0 and disk_result[1].strip() else 0.0

            return {
                "cpu_usage": round(cpu_usage, 2),
                "memory_usage": round(memory_usage, 2),
                "disk_usage": round(disk_usage, 2)
            }

        except Exception as e:
            logger.error(f"收集服务器 {getattr(server_obj, 'name', 'unknown')} 系统指标失败: {e}")
            return {
                "cpu_usage": 0.0,
                "memory_usage": 0.0,
                "disk_usage": 0.0
            }
    
    def get_all_servers_monitoring(self) -> List[Dict[str, Any]]:
        """获取所有服务器的监控数据"""
        return list(self.monitoring_data.values())
    
    def get_server_monitoring(self, server_id: int) -> Optional[Dict[str, Any]]:
        """获取指定服务器的监控数据"""
        return self.monitoring_data.get(server_id)
    
    def set_monitoring_interval(self, interval: int):
        """设置监控间隔"""
        if interval < 5:
            raise ValueError("监控间隔不能小于5秒")
        self.monitoring_interval = interval
        logger.info(f"监控间隔已设置为 {interval} 秒")
    
    def get_monitoring_interval(self) -> int:
        """获取当前监控间隔"""
        return self.monitoring_interval


# 全局监控服务实例
monitoring_service = MonitoringService()
